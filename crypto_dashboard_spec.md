**Numora‑Style Crypto Dashboard: React Architecture & Integration Guide**

---

## 1. Overview

This document outlines the design vision, UI/UX considerations, React component breakdown, chart‑library choices, data‑fetch strategy, and a folder/file scaffold for building a Numora‑style crypto analytics dashboard in React (TypeScript).

---

## 2. UI/UX Design Overview

### 2.1 Theme & Style

- **Dark Mode UI**: Optimized for prolonged viewing, with high contrast and subdued colors.
- **Grayscale Palette**: Dark greys for backgrounds, light greys for text, and accent colors for data highlights.
- **Grid‑Based Layout**: Modular panels arranged in flexible rows/columns.

### 2.2 Layout & Panels

```
┌───────────────────────────────────┐
│ Navbar (Brand | Search | Links | Profile) │
└───────────────────────────────────┘
┌──────────────┬────────────────────────────┬───────────────┐
│ Transactions │     Main Candlestick      │ Portfolio &   │
│ Heatmap      │          Chart            │ Unlocks Pie   │
│              │                            │               │
├──────────────┼────────────────────────────┤ AI Assistant  │
│ Holders      │ Buy/Sell Volume | Long/    │ Chat Panel    │
│ Distribution │ Short Ratio                │               │
└──────────────┴────────────────────────────┴───────────────┘
```

### 2.3 Standout Features

- **Interactive Candlestick Chart** with indicators and timeframes
- **On‑Chain Analytics**: transaction heatmap, holder breakdown, whale vs trader metrics
- **Portfolio Tracker**: live values, allocation breakdown
- **Unlocks Visualization**: doughnut chart of locked vs unlocked tokens
- **Trade Sentiment**: long/short ratio area chart
- **Buy/Sell Volume**: bar chart with net buy metrics
- **AI Assistant**: embedded chat for forecasts and insights

---

## 3. Technical/Dev Considerations

### 3.1 Suggested Stack

- **Frontend**: React + TypeScript
- **Styling**: Tailwind CSS (dark theme utilities) or SCSS partials
- **Charts**: TradingView Lightweight Charts, Recharts, react‑chartjs‑2, react‑calendar‑heatmap
- **State Management**: Zustand or Redux Toolkit
- **Data Fetching**: React Query or SWR
- **Real‑Time**: WebSockets for live price/ratio updates, fallback to polling
- **Routing**: React Router v6
- **Wallet Integration**: ethers.js or web3.js
- **Animations**: Framer Motion for micro‑animations

---

## 4. React Component Architecture

```
App
└── DashboardLayout
    ├── Navbar
    └── DashboardMain
        ├── TokenChartPanel
        ├── TransactionsHeatmap
        ├── HoldersDistribution
        ├── PortfolioPanel
        ├── UnlocksChart
        ├── BuySellVolume
        ├── LongShortRatio
        └── AIAssistant
```

---

## 5. Chart‑Library Integrations

| Feature           | Library                        | File(s)                                                                                         |
| ----------------- | ------------------------------ | ----------------------------------------------------------------------------------------------- |
| Candlestick Chart | TradingView Lightweight Charts | components/TokenChartPanel.tsx                                                                  |
| Heatmap           | react‑calendar‑heatmap         | components/TransactionsHeatmap.tsx                                                              |
| Bar / Area Charts | Recharts                       | components/HoldersDistribution.tsx, components/LongShortRatio.tsx, components/BuySellVolume.tsx |
| Pie / Doughnut    | react‑chartjs‑2                | components/PortfolioPanel.tsx, components/UnlocksChart.tsx                                      |

---

## 6. Data & State Management

```
src/
└── hooks/
    ├── usePriceData.ts       # REST/WebSocket candlestick & price updates
    ├── useHeatmapData.ts     # daily transaction counts
    ├── useHoldersData.ts     # holder-type breakdown
    └── usePortfolioData.ts   # portfolio values & asset allocation
```

- **Global State**: Zustand or Redux Toolkit
- **Fetch Library**: React Query or SWR
- **Real‑Time**: WebSockets, fallback polling every 5–10s

---

## 7. Folder Structure Scaffold

```
src/
├── assets/
│   ├── icons/
│   └── styles/
│       └── dark-theme.css
├── components/
│   ├── AIAssistant.tsx
│   ├── BuySellVolume.tsx
│   ├── HoldersDistribution.tsx
│   ├── LongShortRatio.tsx
│   ├── PortfolioPanel.tsx
│   ├── TokenChartPanel.tsx
│   ├── TransactionsHeatmap.tsx
│   └── UnlocksChart.tsx
├── hooks/
│   ├── useHeatmapData.ts
│   ├── useHoldersData.ts
│   ├── usePortfolioData.ts
│   └── usePriceData.ts
├── layout/
│   ├── DashboardLayout.tsx
│   └── Navbar.tsx
├── pages/
│   └── TokenPage.tsx        # dashboard wrapper per token
├── services/
│   ├── api.ts               # axios REST clients
│   └── ws.ts                # WebSocket connectors
├── utils/
│   ├── constants.ts         # timeframes, endpoints, chart options
│   └── types.ts             # TS interfaces for API data
└── App.tsx
```

---

## 8. Example Code Stubs

### 8.1 TokenChartPanel.tsx

```tsx
import React, { useRef, useEffect } from 'react';
import { createChart, ISeriesApi } from 'lightweight-charts';
import usePriceData from '../hooks/usePriceData';

export default function TokenChartPanel({ symbol }: { symbol: string }) {
  const ref = useRef<HTMLDivElement>(null);
  const priceData = usePriceData(symbol);

  useEffect(() => {
    if (!ref.current || !priceData) return;
    const chart = createChart(ref.current, { width: 600, height: 300 });
    const series: ISeriesApi<'Candlestick'> = chart.addCandlestickSeries();
    series.setData(priceData);
  }, [priceData]);

  return <div ref={ref} />;
}
```

### 8.2 PortfolioPanel.tsx

```tsx
import React from 'react';
import { PieChart, Pie, Tooltip, ResponsiveContainer } from 'recharts';
import usePortfolioData from '../hooks/usePortfolioData';

export default function PortfolioPanel() {
  const { allocation } = usePortfolioData();

  return (
    <ResponsiveContainer width="100%" height={200}>
      <PieChart>
        <Pie data={allocation} dataKey="value" nameKey="asset" outerRadius={80} />
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  );
}
```

---

## 9. Styling & Extras

- Tailwind CSS (dark-mode utilities)
- Framer Motion for animations (panels, chart transitions)
- React Router v6 for dynamic routes
- ethers.js or web3.js for optional wallet/connect




