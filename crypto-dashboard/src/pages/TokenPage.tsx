import React from 'react';
import TokenChartPanel from '../components/TokenChartPanel';
import TransactionsHeatmap from '../components/TransactionsHeatmap';
import HoldersDistribution from '../components/HoldersDistribution';
import PortfolioPanel from '../components/PortfolioPanel';
import UnlocksChart from '../components/UnlocksChart';
import BuySellVolume from '../components/BuySellVolume';
import LongShortRatio from '../components/LongShortRatio';
import AIAssistant from '../components/AIAssistant';

const TokenPage = () => {
  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-3">
        <TransactionsHeatmap />
        <HoldersDistribution />
      </div>
      <div className="col-span-6">
        <TokenChartPanel />
        <div className="grid grid-cols-2 gap-4 mt-4">
          <BuySellVolume />
          <LongShortRatio />
        </div>
      </div>
      <div className="col-span-3">
        <PortfolioPanel />
        <UnlocksChart />
        <AIAssistant />
      </div>
    </div>
  );
};

export default TokenPage;