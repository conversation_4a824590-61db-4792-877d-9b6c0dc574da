import React from 'react';

const Navbar = () => {
  return (
    <nav className="bg-gray-800 p-4 flex justify-between items-center">
      <div className="text-xl font-bold">Numora</div>
      <div>
        <input type="text" placeholder="Search any token" className="bg-gray-700 text-white rounded-md px-4 py-2" />
      </div>
      <div className="flex items-center">
        <a href="#" className="mr-4">AI Signals</a>
        <a href="#" className="mr-4">Stake</a>
        <a href="#" className="mr-4">Portfolio</a>
        <a href="#" className="mr-4">Smart Alerts</a>
      </div>
      <div className="flex items-center">
        <div className="mr-4">Nolan</div>
        <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
      </div>
    </nav>
  );
};

export default Navbar;
