import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YA<PERSON>s, Tooltip, ResponsiveContainer } from 'recharts';

const BuySellVolume = () => {
  const data = [
    { name: 'Sep', buy: 4000, sell: 2400 },
    { name: 'Oct', buy: 3000, sell: 1398 },
    { name: 'Nov', buy: 2000, sell: 9800 },
    { name: 'Dec', buy: 2780, sell: 3908 },
    { name: 'Jan', buy: 1890, sell: 4800 },
    { name: 'Feb', buy: 2390, sell: 3800 },
    { name: 'Mar', buy: 3490, sell: 4300 },
  ];

  return (
    <div className="bg-gray-800 p-4 rounded-md">
      <h2 className="text-lg font-bold mb-4">Buy/Sell Volume</h2>
      <ResponsiveContainer width="100%" height={200}>
        <BarChart data={data}>
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="buy" stackId="a" fill="#82ca9d" />
          <Bar dataKey="sell" stackId="a" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BuySellVolume;
