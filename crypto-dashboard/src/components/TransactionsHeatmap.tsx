import React from 'react';
import CalendarHeatmap from 'react-calendar-heatmap';

const TransactionsHeatmap = () => {
  const data = [
    { date: '2023-01-01', count: 2 },
    { date: '2023-01-02', count: 5 },
    { date: '2023-01-03', count: 1 },
    { date: '2023-01-04', count: 8 },
    { date: '2023-01-05', count: 3 },
    { date: '2023-01-06', count: 6 },
  ];

  return (
    <div className="bg-gray-800 p-4 rounded-md">
      <h2 className="text-lg font-bold mb-4">Transactions Heatmap</h2>
      <CalendarHeatmap
        startDate={new Date('2023-01-01')}
        endDate={new Date('2023-01-06')}
        values={data}
        classForValue={(value) => {
          if (!value) {
            return 'color-empty';
          }
          return `color-scale-${value.count}`;
        }}
      />
    </div>
  );
};

export default TransactionsHeatmap;