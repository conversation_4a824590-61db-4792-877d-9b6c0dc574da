import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ltip, ResponsiveContainer } from 'recharts';

const HoldersDistribution = () => {
  const data = [
    { name: 'Cruisers', value: 20 },
    { name: 'Holders', value: 75 },
    { name: 'Traders', value: 5 },
  ];

  return (
    <div className="bg-gray-800 p-4 rounded-md mt-4">
      <h2 className="text-lg font-bold mb-4">Holders Distribution</h2>
      <ResponsiveContainer width="100%" height={200}>
        <BarChart data={data}>
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="value" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default HoldersDistribution;
