import React from 'react';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJ<PERSON>, ArcElement, Tooltip, Legend } from 'chart.js';

ChartJS.register(Arc<PERSON><PERSON>, Toolt<PERSON>, Legend);

const UnlocksChart = () => {
  const data = {
    labels: ['Total Locked', 'TBD Locked', 'Unlocked', 'Untracked'],
    datasets: [
      {
        data: [13.85, 52.17, 33.98, 0.00],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#E7E9ED'],
        hoverBackgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#E7E9ED'],
      },
    ],
  };

  return (
    <div className="bg-gray-800 p-4 rounded-md mt-4">
      <h2 className="text-lg font-bold mb-4">Unlocks</h2>
      <Doughnut data={data} />
    </div>
  );
};

export default UnlocksChart;
