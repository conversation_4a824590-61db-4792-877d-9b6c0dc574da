import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts';

const PortfolioPanel = () => {
  const data = [
    { name: 'USDT', value: 35 },
    { name: 'ETH', value: 12.5 },
    { name: '<PERSON><PERSON>', value: 20.3 },
    { name: 'UNI', value: 18.2 },
  ];

  return (
    <div className="bg-gray-800 p-4 rounded-md">
      <h2 className="text-lg font-bold mb-4">Portfolio</h2>
      <ResponsiveContainer width="100%" height={200}>
        <PieChart>
          <Pie data={data} dataKey="value" nameKey="name" outerRadius={80} fill="#8884d8" />
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PortfolioPanel;
