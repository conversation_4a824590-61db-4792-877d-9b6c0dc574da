import React from 'react';
import { AreaChart, Area, XAxis, <PERSON>A<PERSON>s, Tooltip, ResponsiveContainer } from 'recharts';

const LongShortRatio = () => {
  const data = [
    { name: '5 Jul', value: 1.1 },
    { name: '6 Jul', value: 1.12 },
    { name: '7 Jul', value: 1.15 },
    { name: '8 Jul', value: 1.18 },
    { name: '9 Jul', value: 1.16 },
    { name: '10 Jul', value: 1.19 },
    { name: '11 Jul', value: 1.2 },
  ];

  return (
    <div className="bg-gray-800 p-4 rounded-md">
      <h2 className="text-lg font-bold mb-4">Long/Short Ratio</h2>
      <ResponsiveContainer width="100%" height={200}>
        <AreaChart data={data}>
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default LongShortRatio;
