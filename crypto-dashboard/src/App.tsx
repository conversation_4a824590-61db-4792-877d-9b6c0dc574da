import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import DashboardLayout from './layout/DashboardLayout';
import TokenPage from './pages/TokenPage';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<DashboardLayout />}>
          <Route index element={<TokenPage />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;