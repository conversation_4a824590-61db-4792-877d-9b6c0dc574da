{"name": "crypto-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "chart.js": "^4.5.0", "ethers": "^6.15.0", "framer-motion": "^12.23.12", "lightweight-charts": "^5.0.8", "postcss-nesting": "^13.0.2", "react": "^19.1.1", "react-calendar-heatmap": "^1.10.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-scripts": "5.0.1", "recharts": "^3.1.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^5.0.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-calendar-heatmap": "^1.9.0", "@types/recharts": "^1.8.29", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}}